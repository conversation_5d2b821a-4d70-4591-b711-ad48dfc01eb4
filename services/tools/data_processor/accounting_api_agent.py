"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class AccountingApiAgent
* @description Tool responsible for handling QuickBooks API to perform accounting tasks.
* <AUTHOR>
"""

from datetime import datetime, timed<PERSON><PERSON>
import json
from logging import Logger
import os
import traceback
from zoneinfo import ZoneInfo
import pandas as pd
from utils.logger import get_debug_logger
from models.conversation import Conversation
from services.tools.data_processor.data_processor_agent import DataProcessorAgent
from utils.constant import AgentState, AnalysisStep
from utils.metalake import (
    intelligent_text_match,
    load_data,
    quickbooks_create_bill,
    quickbooks_create_vendor,
    quickbooks_update_vendor,
    save_data,
    quickbooks_create_purchase,
    quickbooks_upload_attachment,
    quickbooks_query_object,
    quickbooks_update_purchase,
    quickbooks_get_company_currency,
)

from services.shared.python_code_agent import AccountingApiCodeAgent
from utils.misc_utils import convert_currency, get_table_headers_and_rows_from_csv, csv_to_markdown
from services.tools.base_tool import ToolOutput


class AccountingApiAgent(DataProcessorAgent):
    """
    Agentic Python workflow agent that manages the state transitions for solving a user question using Python code, code review, execution, and data review.
    Input: user question + data file names list
    """

    def __init__(self):
        super().__init__()
        # Register state handler classes
        self.state_handler_class_map[AgentState.DATA_PROCESSING] = AccountingApiCodeAgent
        self.company_currency = None
        # Load company currency from QuickBooks
        self.company_currency = quickbooks_get_company_currency()

    """
    additional_data contains the following:
    1. api_task: The task to perform. Eg: add_expense, add_bill, add_vendor
    """

    def execute_task(
        self,
        conversation: Conversation,
        data_source_name: str,
        data_file_name_list: list,
        task_instruction: str,
        base_log_path: str = "",
        additional_data: dict = None,
        event_loop=None,
    ):
        conversation.active_session_send_analysis_step(AnalysisStep.ACCOUNTING_API)
        chat_log = get_debug_logger(
            f"chat_{conversation.chat_id}", f"./logs/{base_log_path}chat_{conversation.chat_id}.log"
        )
        # Load first 5 records from given data files (CSV) and give as additional input to Python code agent
        # First check the existance of all data files and inform back if any of them are missing
        non_existing_files_list = []
        data_file_path_list = []
        for data_file_name in data_file_name_list:
            data_file_path = f"storage/public/{conversation.chat_id}/files/{data_file_name}"
            if not os.path.exists(data_file_path):
                # Now check if the file exists in attachments folder
                data_file_path = f"storage/public/{conversation.chat_id}/attachments/{data_file_name}"
                if not os.path.exists(data_file_path):
                    non_existing_files_list.append(data_file_name)
                else:
                    data_file_path_list.append(data_file_path)
            else:
                data_file_path_list.append(data_file_path)
        if non_existing_files_list:
            return ToolOutput(
                display_output=f"Failed to find following mentioned files: {non_existing_files_list}\n\nPlease check the file names and try again.\n\n",
                result_text_list=[tuple([f"Files not found: {non_existing_files_list}", False])],
            )

        # If this is one of the defined tasks, then call the pre-defined function
        output_dict = {}
        task_executed_with_predefined_function = False
        if additional_data and "api_task" in additional_data:
            api_task = additional_data["api_task"]
            if api_task == "add_expenses":
                output_dict = self.add_expenses(
                    conversation, data_source_name, data_file_path_list, task_instruction, chat_log
                )
                task_executed_with_predefined_function = True
            elif api_task == "update_expenses":
                output_dict = self.update_expenses(
                    conversation, data_source_name, data_file_path_list, task_instruction, chat_log
                )
                task_executed_with_predefined_function = True
            # elif api_task == "update_expense_lines":
            #     output_dict = self.update_expense_lines(conversation, data_file_path_list, task_instruction, chat_log)
            #     task_executed_with_predefined_function = True

            elif api_task == "query_transactions":
                # Get the transaction type from the "data_type_list" in additional_data
                if "data_type_list" not in additional_data or not additional_data["data_type_list"]:
                    return ToolOutput(
                        display_output="Failed to find data_type_list in additional_data. Please check the additional_data and try again.",
                        result_text_list=[
                            (
                                "Failed to find data_type_list in additional_data. Please check the additional_data and try again.",
                                False,
                            )
                        ],
                    )
                # Get from_date and to_date from additional_data
                if "from_date" not in additional_data or "to_date" not in additional_data:
                    return ToolOutput(
                        display_output="Failed to find from_date or to_date in additional_data. Please check the additional_data and try again.",
                        result_text_list=[
                            (
                                "Failed to find from_date or to_date in additional_data. Please check the additional_data and try again.",
                                False,
                            )
                        ],
                    )
                transaction_type = additional_data["data_type_list"][0]
                from_date = additional_data["from_date"]
                to_date = additional_data["to_date"]
                output_dict = self.query_transactions(
                    conversation, transaction_type, chat_log, from_date=from_date, to_date=to_date
                )
                task_executed_with_predefined_function = True

        if not task_executed_with_predefined_function:
            # Go through each CSV file and convert them to .dat file and get preview data
            preview_data_str = ""
            for data_file_path in data_file_path_list:
                data_file_name = data_file_path.split("/")[-1]
                # Get preview only from .csv
                if not data_file_path.endswith(".csv"):
                    continue
                # headers, rows = get_table_headers_and_rows_from_csv(data_file_path, 5, chat_log)
                # # Save to .dat using save_data - first merge headers and rows
                # data_records = [headers] + [rows]
                # save_data(conversation.chat_id, data_file_name, data_records)
                markdown_data, truncated, row_count = csv_to_markdown(data_file_path, 10)
                preview_data_str += (
                    "\n\nData file name: " + data_file_name + ": First 10 rows:\n\n" + markdown_data + "\n\n"
                )

            input_data = {
                "user_question": task_instruction,
                "data_file_names": data_file_name_list,
                "data_input": preview_data_str,
            }
            # Set the correct agent state to start with
            conversation.agent_state = AgentState.DATA_PROCESSING
            output_dict = self.start(
                conversation,
                data_source_name,
                input_data,
                base_log_path,
            )

        tool_result_to_summarize = ""
        tool_result_to_not_summarize = ""
        # Get output CSV file names
        csv_file_names = []
        data_file_names = []
        output_file_preview_str = ""
        for file_path in output_dict.get("csv_paths", []):
            # check if the extension is csv, if not then skip
            if not file_path.endswith(".csv"):
                continue
            csv_file_names.append(file_path.split("/")[-1])
            markdown_data, truncated, row_count = csv_to_markdown(file_path, 10)
            output_file_preview_str += (
                "\n\nOutput file name: "
                + str(file_path.split("/")[-1])
                + ": First 10 rows:\n\n"
                + markdown_data
                + "\n\n"
            )
            # Update .dat file name list if exists
            data_file_name = file_path.split("/")[-1].replace(".csv", ".dat")
            if os.path.exists(f"storage/public/{conversation.chat_id}/{data_file_name}"):
                data_file_names.append(data_file_name)

        if "result_markdown" in output_dict:
            tool_result_to_summarize += "Result:\n\n" + output_dict["result_markdown"] + "\n\n"
            tool_result_to_not_summarize += "Result:\n\n" + output_dict["result_markdown"] + "\n\n"
        if output_dict.get("python_code", ""):
            tool_result_to_summarize += "Python code:\n\n```python\n" + output_dict["python_code"] + "\n````\n\n"

        tool_result_to_summarize += (
            output_dict.get("execution_result", "") + "\n\nOutput csv files:\n\n" + output_file_preview_str
        )
        if data_file_names:
            tool_result_to_summarize += "\n\nOutput data files: " + str(data_file_names) + "\n\n"

        tool_result_to_not_summarize += (
            output_dict.get("execution_result", "") + "\n\n" + "Output csv files: " + str(csv_file_names)
        )
        if data_file_names:
            tool_result_to_not_summarize += "\n\nOutput data files: " + str(data_file_names) + "\n\n"

        tool_results_list = [
            tuple([tool_result_to_summarize, True]),
            tuple([tool_result_to_not_summarize, False]),
        ]

        # output_display = (
        #     "Result: " + output_dict.get("execution_result", "") + "\n\n" + "Output CSV files: " + str(csv_file_names)
        # )
        return ToolOutput(display_output=tool_result_to_summarize, result_text_list=tool_results_list)

    # Pre-defined functions
    """
    Description: Add list of expenses to QuickBooks
    Input: CSV file with following columns:
    1. Date
    2. Vendor
    3. Account
    4. Amount
    5. Memo
    6. Attachments (optional)
    Output: List of csv files with reported status of each expense recording
    """

    def add_expenses(
        self, conversation: Conversation, source: str, data_file_path_list: list, task_instruction, chat_log
    ):
        # First validate whether purchases.csv and purchase_lines.csv are present in the data_file_path_list
        purchases_csv_path = None
        purchase_lines_csv_path = None
        for data_file_path in data_file_path_list:
            if data_file_path.endswith("purchases.csv"):
                purchases_csv_path = data_file_path
            elif data_file_path.endswith("purchase_lines.csv"):
                purchase_lines_csv_path = data_file_path

        if not purchases_csv_path:
            return {
                "execution_result": "Failed to find purchases.csv file. Please check the file names and try again.",
                "csv_paths": [],
            }
        if not purchase_lines_csv_path:
            return {
                "execution_result": "Failed to find purchase_lines.csv file. Please check the file names and try again.",
                "csv_paths": [],
            }

        return self._add_entities(
            conversation, "Purchase", source, purchases_csv_path, purchase_lines_csv_path, task_instruction, chat_log
        )

    """
    Description: Add list of bills to QuickBooks
    Input: CSV files with following columns:
    1. Date
    2. Vendor
    3. AP Account
    4. Line Amounts
    5. Line Expense Accounts
    6. Line Tax Codes
    7. Attachments (optional)
    8. Term (optional)
    9. Due Date (optional)
    Output: List of csv files with reported status of each bill recording
    """

    def add_bills(
        self, conversation: Conversation, source: str, data_file_path_list: list, task_instruction, chat_log
    ):
        # First validate whether bills.csv and bill_lines.csv are present in the data_file_path_list
        bills_csv_path = None
        bill_lines_csv_path = None
        for data_file_path in data_file_path_list:
            if data_file_path.endswith("bills.csv"):
                bills_csv_path = data_file_path
            elif data_file_path.endswith("bill_lines.csv"):
                bill_lines_csv_path = data_file_path
        if not bills_csv_path:
            return {
                "execution_result": "Failed to find bills.csv file. Please check the file names and try again.",
                "csv_paths": [],
            }
        if not bill_lines_csv_path:
            return {
                "execution_result": "Failed to find bill_lines.csv file. Please check the file names and try again.",
                "csv_paths": [],
            }
        return self._add_entities(conversation, "Bill", source, data_file_path_list, task_instruction, chat_log)

    """
    Generic function to add entities to QuickBooks
    """

    def _add_entities(
        self,
        conversation: Conversation,
        entity_type: str,
        source: str,
        header_file_path: str,
        line_item_file_path: str,
        task_instruction,
        chat_log,
    ):
        # Validate source - should be either 'invoice', 'receipt' or 'statement'
        if source not in ["invoice", "receipt", "statement"]:
            return {
                "execution_result": "Invalid source. Should be either 'invoice', 'receipt' or 'statement'.",
                "csv_paths": [],
            }

        chat_id = conversation.chat_id

        # --- Load Data ---
        try:
            purchases_df = load_data(chat_id, "purchases.csv")
        except Exception as e:
            purchases_df = pd.DataFrame()
            load_purchases_err = str(e)

        try:
            lines_df = load_data(chat_id, "purchase_lines.csv")
        except Exception as e:
            lines_df = pd.DataFrame()
            load_lines_err = str(e)

        results = []
        created_ids = []
        updated_ids = []
        n_duplicates = 0
        n_updates = 0
        # Track the first created at time - in local timezone - in string format: YYYY-MM-DDTHH:MM:SS-HH:MM
        company_tz = ZoneInfo("America/Winnipeg")
        local_time = datetime.now().astimezone(company_tz)
        # Format in ISO 8601 with offset
        first_created_cutoff = local_time.isoformat()

        csv_output_file_paths = []

        if purchases_df.empty:
            # No purchases to process
            # Output empty result with explanation
            out_path = "files/results_add_purchases.csv"
            pd.DataFrame(
                [
                    {
                        "purchase_key": None,
                        "purchase_id": None,
                        "status": "Failed",
                        "error": "No purchases to process.",
                        "attachment_files_attempted": 0,
                        "attachment_success_count": 0,
                        "attachment_error_count": 0,
                    }
                ]
            ).to_csv(f"storage/public/{chat_id}/{out_path}", index=False)
            chat_log.info("0 success, 0 failed — see results_add_purchases.csv")
            return {
                "execution_result": "0 success, 0 failed — see results_add_purchases.csv",
                "csv_paths": [f"storage/public/{chat_id}/files/results_add_purchases.csv"],
            }

        # For auditability
        purchases_df = purchases_df.sort_values("purchase_key")
        if not lines_df.empty:
            lines_df = lines_df.sort_values(["purchase_key", "line_number"])
        else:
            # No lines for any purchases
            lines_df = pd.DataFrame(
                columns=[
                    "purchase_key",
                    "line_number",
                    "detail_type",
                    "line_amount",
                    "description",
                    "expense_account_id",
                    "tax_code_id",
                ]
            )

        for _, row in purchases_df.iterrows():
            # --- Build payload ---
            payload_res = self._quickbooks_create_purchase_object(source, row, lines_df, chat_log)
            if not payload_res.get("is_success"):
                results.append(
                    {
                        "purchase_key": row.get("purchase_key"),
                        "purchase_id": None,
                        "status": "Data validation failed",
                        "success": False,
                        "error": payload_res.get("error_message", "Unknown error"),
                        "attachment_files_attempted": 0,
                        "attachment_success_count": 0,
                        "attachment_error_count": 0,
                    }
                )
                continue
            payload = payload_res.get("payload")
            purchase_key = row.get("purchase_key")
            attachment_file_name = row.get("attachment_file_name", "")
            attachment_needed = attachment_file_name not in [None, "", "N/A", "n/a"]
            attach_attempted = 0
            attach_success = 0
            attach_error = 0
            err = ""
            created_or_updated_id = ""
            # --- Find matching existing purchase if any ---
            existing_purchase = self._find_matched_existing_item(
                "Purchase", payload, source == "statement", created_ids, chat_id, chat_log
            )
            if existing_purchase is not None:
                res = self._quickbooks_purchase_update_with_existing(
                    source, purchase_key, payload, existing_purchase, chat_log
                )
                if not res.get("success"):
                    err = res.get("message", "Unknown error")
                if res["is_update_needed"]:
                    n_duplicates += 1
                if res["success"]:
                    created_or_updated_id = res["createdId"]
                    updated_ids.append(res["createdId"])
                    n_updates += 1
                else:
                    chat_log.error(f"Failed to update existing purchase for purchase {purchase_key}: {err}")
                    res["status"] = "Failed to update existing purchase"
                # Attachment not needed if purchase is already added via a receipt
                if "source=receipt" in existing_purchase.get("PrivateNote", ""):
                    attachment_needed = False
            else:
                # --- Create Purchase with QuickBooks API ---
                try:
                    # Remove original_amount and TotalAmt from the payload if exists (original amount in Line)
                    payload = {k: v for k, v in payload.items() if k not in ["TotalAmt"]}
                    for line in payload["Line"]:
                        del line["original_amount"]
                    res = quickbooks_create_purchase(payload)
                except Exception as e:
                    res = {"success": False, "message": str(e)}
                if res.get("success"):
                    created_or_updated_id = res.get("createdId")
                    res["status"] = "Expense created successfully"
                    created_ids.append(created_or_updated_id)
                    chat_log.debug(f"Created new purchase {created_or_updated_id} for purchase {purchase_key}")
                else:
                    res["status"] = "Expense creation failed"
                    err = (str(err) + "; " if err else "") + f"Purchase creation failed: {res.get('message')}"

            # --- Upload Attachment if needed ---
            if attachment_needed and created_or_updated_id:
                chat_log.debug(
                    f"Uploading attachment {attachment_file_name} for purchase {purchase_key} with id {created_or_updated_id}"
                )
                attach_attempted = 1
                try:
                    upload_res = quickbooks_upload_attachment(
                        chat_id, attachment_file_name, "Purchase", created_or_updated_id
                    )
                    if upload_res.get("success"):
                        attach_success = 1
                    else:
                        attach_error = 1
                        err = (
                            str(err) + "; " if err else ""
                        ) + f"Attachment: {upload_res.get('message','Unknown error')}"
                except Exception as e:
                    attach_error = 1
                    err = (
                        str(err) + "; " if err else ""
                    ) + f"Attachment exception: {str(e)}: \n{traceback.format_exc()}"

            results.append(
                {
                    "purchase_key": purchase_key,
                    "purchase_id": created_or_updated_id,
                    "status": res["status"],
                    "success": res.get("success"),
                    "error": res["message"] if not res.get("success") and "message" in res else err,
                    "attachment_files_attempted": attach_attempted,
                    "attachment_success_count": attach_success,
                    "attachment_error_count": attach_error,
                }
            )

        # Output results CSV
        file_name = "results_add_purchases.csv"
        out_path = f"files/{file_name}"
        # full_path = f"storage/public/{chat_id}/{out_path}"
        results_df = pd.DataFrame(results)
        # chat_log.info(f"Results file created at {full_path}:\n{results_df.head(5)}")
        # results_df.to_csv(full_path, index=False)
        # csv_output_file_paths.append(full_path)
        # Add to the conversation data reference set
        # conversation.data_reference_set.add(file_name)
        result_markdown = results_df.to_markdown(index=False)

        # Review created and updated entries
        created_entries_file_name, n_verified = self.review_created_entries(
            chat_id, "Purchase", first_created_cutoff, created_ids, chat_log
        )
        if created_entries_file_name:
            conversation.data_reference_set.add(created_entries_file_name)
            csv_output_file_paths.append(f"storage/public/{chat_id}/files/{created_entries_file_name}")
        updated_entries_file_name, n_update_verified = self.review_updated_entries(
            chat_id, "Purchase", first_created_cutoff, updated_ids, chat_log
        )
        n_verified += n_update_verified
        if updated_entries_file_name:
            conversation.data_reference_set.add(updated_entries_file_name)
            csv_output_file_paths.append(f"storage/public/{chat_id}/files/{updated_entries_file_name}")
        # Print summary
        n_success = sum(r["success"] for r in results)
        n_failed = sum(not r["success"] for r in results)
        chat_log.info(
            f"{n_success} success, {n_failed} failed, {n_verified} verified line items, {n_duplicates} duplicates, {n_updates} updates, result summary: \n{result_markdown}"
        )

        return {
            "execution_result": (
                f"{n_success} success, {n_failed} failed, {n_duplicates} duplicates, {n_updates} updates, expense addition summary: \n{result_markdown} \n{n_verified} verified line items in QBO.\n\nQuickBooks Home Currency: {self.company_currency}"
                if created_entries_file_name or updated_entries_file_name
                else "Verification of created entries failed."
            ),
            "result_markdown": result_markdown,
            "csv_paths": csv_output_file_paths,
        }

    """
    Description: Query transactions from QuickBooks
    Input: 
    1. transaction_type: Type of the transaction to query (e.g. Bill, Purchase, JournalEntry, Transfer, etc.)
    2. from_date: Start date of the transaction date filter
    3. to_date: End date of the transaction date filter
    Output: CSV file with the queried transactions
    """

    def query_transactions(
        self,
        conversation: Conversation,
        transaction_type: str,
        chat_log: Logger,
        id_list: list = [],
        from_date: str = "",
        to_date: str = "",
    ):
        # Validate transaction type (currently only 'Purchase' is supported)
        if transaction_type != "Purchase":
            return {
                "execution_result": "Invalid transaction type. Currently only 'Purchase' is supported.",
                "csv_paths": [],
                "records": {},
            }
        query = ""
        if id_list or from_date or to_date:
            query += "WHERE "
            if id_list:
                # Make sure id list is string list
                id_list = [("'" + str(i) + "'") for i in id_list]
                query += f"Id IN ({','.join(id_list)})"
            if from_date and to_date:
                if id_list:
                    query += " AND "
                query += f"TxnDate >= '{from_date}' AND TxnDate <= '{to_date}'"

        res = quickbooks_query_object(transaction_type, query)
        if not res.get("success"):
            return {
                "execution_result": f"Failed to query transactions. Error: {res.get('message')}",
                "csv_paths": [],
                "records": {},
            }
        # Write the data records to a CSV file
        data_records = res["data"].get("QueryResponse", {}).get(transaction_type, [])
        if not data_records:
            return {
                "execution_result": "No transactions found for the given date range.",
                "csv_paths": [],
                "records": {},
            }
        df = self._parse_created_entries(transaction_type, data_records, chat_log)
        file_name = f"queried_{transaction_type.lower()}_records.csv"
        out_path = f"storage/public/{conversation.chat_id}/files/{file_name}"
        df.to_csv(out_path, index=False)
        # Write parsed records to .dat file as well
        dat_file_name = file_name.replace(".csv", ".dat")
        parsed_records = df.to_dict("records")
        save_data(conversation.chat_id, dat_file_name, parsed_records)
        # Build dictionary with id as key and record as value
        records_dict = {r["Id"]: r for r in data_records}
        return {
            "execution_result": f"Query successful. See {file_name} for the results.",
            "csv_paths": [out_path],
            "records": records_dict,
        }

    def _quickbooks_purchase_update_with_existing(
        self, input_source, purchase_key, payload, existing_purchase, chat_log
    ):
        res = {
            "is_update_needed": False,
            "success": False,
            "status": "Expense already exists",
            "message": "Unknown error",
            "createdId": "",
        }
        try:
            if input_source == "statement":
                # If input source is statement and existing purchase is from receipt, then update the payment account id if different
                if "source=receipt" in existing_purchase.get("PrivateNote", ""):
                    if existing_purchase["AccountRef"]["value"] != payload["AccountRef"]["value"]:
                        chat_log.debug(
                            f"Updating payment account for existing purchase {existing_purchase['Id']} for purchase {purchase_key}: {existing_purchase['AccountRef']['value']} -> {payload['AccountRef']['value']}"
                        )
                        existing_lines = existing_purchase["Line"]
                        # If the existing purchase total is different to total amount of the statement record (due to currency difference), then update the line items as well
                        if existing_purchase["TotalAmt"] != payload["TotalAmt"]:
                            convert_ratio = payload["TotalAmt"] / existing_purchase["TotalAmt"]
                            chat_log.debug(
                                f"Updating line items for existing purchase {existing_purchase['Id']} for purchase {purchase_key}: {existing_purchase['TotalAmt']} -> {payload['TotalAmt']} (Ratio: {convert_ratio})"
                            )
                            for line in existing_lines:
                                line["Amount"] = line["Amount"] * convert_ratio

                        update_payload = {
                            "Id": existing_purchase["Id"],
                            "sparse": True,
                            "PaymentType": payload["PaymentType"],
                            "AccountRef": payload["AccountRef"],
                            "SyncToken": existing_purchase["SyncToken"],
                            "Line": existing_lines,
                        }
                        # --- Update Purchase API call ---
                        try:
                            res = quickbooks_update_purchase(update_payload)
                            res["is_update_needed"] = True

                        except Exception as e:
                            res["message"] = str(e)
                        if res.get("success"):
                            res["status"] = "Updated the expense added via receipt"
                            chat_log.debug(
                                f"Updated existing purchase {existing_purchase['Id']} for purchase {purchase_key}"
                            )
                        else:
                            chat_log.error(
                                f"Failed to update existing purchase {existing_purchase['Id']} for purchase {purchase_key}: {res.get('message')}"
                            )
                    else:
                        # Nothing to update
                        chat_log.debug(
                            f"No change needed for existing purchase {existing_purchase['Id']} for purchase {purchase_key} already added from receipt."
                        )
                        res["success"] = True
                else:
                    # This is a duplicate statement input - skip
                    chat_log.debug(f"Duplicate statement input found for purchase {purchase_key}. Skipping...")
                    res["success"] = True
            else:
                # If input from receipt and existing purchase is from statement - update line items, transaction date from the receipt
                if "source=statement" in existing_purchase.get("PrivateNote", ""):
                    # Line item amounts recalculate based on total amount given in statement record (Due to currency difference) if existing purchase total is different to total amount of the statement record
                    if existing_purchase["TotalAmt"] != payload["TotalAmt"]:
                        convert_ratio = existing_purchase["TotalAmt"] / payload["TotalAmt"]
                        chat_log.debug(
                            f"Updating line items for existing purchase {existing_purchase['Id']} for purchase {purchase_key}: {payload['TotalAmt']} -> {existing_purchase['TotalAmt']} (Ratio: {convert_ratio})"
                        )
                        # Correct the TotalAmt in the payload as well
                        payload["TotalAmt"] = existing_purchase["TotalAmt"]
                        for line in payload["Line"]:
                            # Always use original amount for conversion because "Amount" field is already converted to company currency in previous steps
                            if "original_amount" not in line:
                                continue
                            line["Amount"] = line["original_amount"] * convert_ratio
                            del line["original_amount"]

                    update_payload = {
                        "Id": existing_purchase["Id"],
                        "sparse": True,
                        "SyncToken": existing_purchase["SyncToken"],
                        "PaymentType": existing_purchase["PaymentType"],
                        "AccountRef": existing_purchase["AccountRef"],
                        "Line": payload["Line"],
                        "TxnDate": payload["TxnDate"],
                        "GlobalTaxCalculation": payload["GlobalTaxCalculation"],
                        "EntityRef": payload["EntityRef"],
                    }
                    # --- Update Purchase API call ---
                    try:
                        res = quickbooks_update_purchase(update_payload)
                        res["is_update_needed"] = True

                    except Exception as e:
                        res["message"] = str(e)
                    if res.get("success"):
                        res["status"] = "Updated the expense added via statement"
                        chat_log.debug(
                            f"Updated existing purchase {existing_purchase['Id']} for purchase {purchase_key}"
                        )
                    else:
                        chat_log.error(
                            f"Failed to update existing purchase {existing_purchase['Id']} for purchase {purchase_key}: {res.get('message')}"
                        )
                else:
                    # Duplicate receipt input - skip
                    chat_log.debug(f"Duplicate receipt input found for purchase {purchase_key}. Skipping...")
                    res["success"] = True
        except Exception as e:
            res["message"] = str(e)
            chat_log.error(f"Error updating existing purchase: {e}: \n{traceback.format_exc()}")

        return res

    def _quickbooks_create_purchase_object(self, source, purchase_row, lines_df, chat_log):
        purchase_key = purchase_row.get("purchase_key")
        status = "Failed"
        created_id = None
        err = ""
        attach_attempted = 0
        attach_success = 0
        attach_error = 0

        # --- Validate header fields ---
        payment_type = purchase_row.get("payment_type")
        account_id = purchase_row.get("payment_account_id")
        transaction_date = purchase_row.get("transaction_date")
        total_amount = purchase_row.get("total_amount")
        currency_code = purchase_row.get("currency", "")
        global_tax_calculation = purchase_row.get("global_tax_calculation", "")
        payee_keyword = purchase_row.get("payee_name", "")
        payee_address = purchase_row.get("payee_address", "")
        payload = {}
        # Find the payee id - either by matching with existing vendor/customer/employee or creating a new vendor
        payee_id = self.find_payee(payee_keyword, payee_address, chat_log, source)
        if not purchase_key:
            err = "Missing purchase_key"
        elif not payment_type:
            err = "Missing payment_type"
        elif not account_id:
            err = "Missing payment_account_id"
        else:
            # --- Collect and validate lines ---
            lines = lines_df[lines_df["purchase_key"] == purchase_key]
            if lines.empty:
                err = f"No line items for purchase_key {purchase_key}"
            else:
                line_objs = []
                for _, lrow in lines.iterrows():
                    detail_type = lrow.get("detail_type", "AccountBasedExpenseLineDetail")
                    line_amount = lrow.get("line_amount")
                    description = lrow.get("description", "")
                    expense_account_id = lrow.get("expense_account_id")
                    tax_code_id = lrow.get("tax_code_id", None)

                    # Validate line fields
                    if line_amount in [None, ""]:
                        err = f"Missing line_amount on line {lrow.get('line_number')}"
                        break
                    if not expense_account_id:
                        err = f"Missing expense_account_id on line {lrow.get('line_number')}"
                        break
                    try:
                        line_amount_val = float(line_amount)
                        line_amount_original = line_amount_val  # Before currency conversion
                        # convert to the company currency in case of receipts
                        if source == "receipt":
                            if not currency_code or not self.company_currency:
                                chat_log.warning(
                                    f"Missing currency_code or company currency for purchase {purchase_key}"
                                )
                            else:
                                conversion_res = convert_currency(
                                    line_amount_val, currency_code, self.company_currency, chat_log
                                )
                                if conversion_res.get("is_success"):
                                    line_amount_val = conversion_res.get("converted_amount")
                                else:
                                    chat_log.warning(
                                        f"Failed to convert currency for line {lrow.get('line_number')}: {conversion_res.get('error_message')}"
                                    )
                    except:
                        err = f"Invalid line_amount on line {lrow.get('line_number')}"
                        break
                    line_obj = {
                        "DetailType": detail_type,
                        "Amount": line_amount_val,
                        "original_amount": line_amount_original,  # This should be excluded from the payload that use for adding or updating calls
                        "AccountBasedExpenseLineDetail": {"AccountRef": {"value": str(expense_account_id)}},
                        "LineNum": (int(lrow.get("line_number")) if lrow.get("line_number") is not None else None),
                    }
                    if description:
                        line_obj["Description"] = description
                    if tax_code_id:
                        line_obj["AccountBasedExpenseLineDetail"]["TaxCodeRef"] = {"value": str(tax_code_id)}
                    # Strip None
                    line_obj = {k: v for k, v in line_obj.items() if v is not None}
                    line_obj["AccountBasedExpenseLineDetail"] = {
                        k: v for k, v in line_obj["AccountBasedExpenseLineDetail"].items() if v is not None
                    }
                    line_objs.append(line_obj)
                if not err:
                    # --- Build header payload ---
                    payload = {
                        "PaymentType": payment_type,
                        "AccountRef": {"value": str(account_id)},
                        "TotalAmt": total_amount,
                        "Line": line_objs,
                        "PrivateNote": "source=" + source,
                    }
                    # Optional fields
                    if transaction_date not in [None, "", pd.NaT]:
                        payload["TxnDate"] = str(transaction_date)
                    if purchase_row.get("payment_method_id") not in [None, ""]:
                        payload["PaymentMethodRef"] = {"value": str(purchase_row.get("payment_method_id"))}
                    if global_tax_calculation not in [None, ""]:
                        payload["GlobalTaxCalculation"] = global_tax_calculation
                    if payee_id:
                        payload["EntityRef"] = {"value": str(payee_id)}
                    # Remove empties
                    payload = {k: v for k, v in payload.items() if v is not None}

        return {
            "is_success": not err,
            "payload": payload,
            "error_message": "Problem processing the purchase with key " + purchase_key + ": " + str(err),
        }

    def _quickbooks_create_bill_object(self, source, bill_row, lines_df, chat_log):
        bill_key = bill_row.get("bill_key")
        status = "Failed"

        # --- Validate header fields ---
        vendor_name = bill_row.get("vendor_name")
        ap_account_id = bill_row.get("ap_account_id")
        transaction_date = bill_row.get("transaction_date")
        due_date = bill_row.get("due_date", "")
        payment_term = bill_row.get("payment_term", "")
        total_amount = bill_row.get("total_amount")
        currency_code = bill_row.get("currency", "")
        global_tax_calculation = bill_row.get("global_tax_calculation", "")
        payload = {}
        vendor_id = self.find_payee(vendor_name, "", chat_log, source, ["Vendor"])
        if not vendor_id:
            err = f"Failed to find vendor for bill_key {bill_key}"
            return {
                "is_success": False,
                "payload": payload,
                "error_message": err,
            }
        # --- Collect and validate lines ---
        lines = lines_df[lines_df["bill_key"] == bill_key]
        if lines.empty:
            err = f"No line items for bill_key {bill_key}"
        else:
            line_objs = []
            for _, lrow in lines.iterrows():
                detail_type = lrow.get("detail_type", "AccountBasedExpenseLineDetail")
                line_amount = lrow.get("line_amount")
                description = lrow.get("description", "")
                expense_account_id = lrow.get("expense_account_id")
                tax_code_id = lrow.get("tax_code_id", None)

                # Validate line fields
                if not line_amount:
                    err = f"Missing line_amount on line {lrow.get('line_number')}"
                    break
                if not expense_account_id:
                    err = f"Missing expense_account_id on line {lrow.get('line_number')}"
                    break
                # Convert line amount to the company currency
                try:
                    line_amount_val = float(line_amount)
                    line_amount_original = line_amount_val  # Before currency conversion
                    if currency_code and self.company_currency and currency_code != self.company_currency:
                        conversion_res = convert_currency(
                            line_amount_val, currency_code, self.company_currency, chat_log
                        )
                        if conversion_res.get("is_success"):
                            line_amount_val = conversion_res.get("converted_amount")
                        else:
                            chat_log.warning(
                                f"Failed to convert currency for line {lrow.get('line_number')}: {conversion_res.get('error_message')}"
                            )
                except:
                    err = f"Invalid line_amount on line {lrow.get('line_number')}"
                    break
                line_obj = {
                    "DetailType": detail_type,
                    "Amount": line_amount_val,
                    "original_amount": line_amount_original,  # This should be excluded from the payload that use for adding or updating calls
                    "AccountBasedExpenseLineDetail": {"AccountRef": {"value": str(expense_account_id)}},
                    "LineNum": (int(lrow.get("line_number")) if lrow.get("line_number") is not None else None),
                }
                if description:
                    line_obj["Description"] = description
                if tax_code_id:
                    line_obj["AccountBasedExpenseLineDetail"]["TaxCodeRef"] = {"value": str(tax_code_id)}
                # Strip None
                line_obj = {k: v for k, v in line_obj.items() if v is not None}
                line_obj["AccountBasedExpenseLineDetail"] = {
                    k: v for k, v in line_obj["AccountBasedExpenseLineDetail"].items() if v is not None
                }
                line_objs.append(line_obj)
            if not err:
                # --- Build header payload ---
                payload = {
                    "VendorRef": {"value": str(vendor_id)},
                    "TotalAmt": total_amount,
                    "Line": line_objs,
                    "PrivateNote": "source=" + source,
                }
                # Optional fields
                if transaction_date not in [None, "", pd.NaT]:
                    payload["TxnDate"] = str(transaction_date)
                if due_date not in [None, "", pd.NaT]:
                    payload["DueDate"] = str(due_date)
                if payment_term not in [None, "", pd.NaT]:
                    payload["PaymentTermRef"] = {"value": str(payment_term)}
                if global_tax_calculation not in [None, ""]:
                    payload["GlobalTaxCalculation"] = global_tax_calculation

        return {
            "is_success": not err,
            "payload": payload,
            "error_message": "Problem processing the bill with key " + bill_key + ": " + str(err),
        }

    def _find_matched_existing_item(
        self,
        entity_type: str,
        item_data_object: dict,
        is_from_statement: bool,
        exclude_item_ids: list,
        chat_id,
        chat_log,
    ):
        # First load existing items for the date range
        items_in_range = []
        # If the item data is from statement, then check with records starting from 5 days before the transaction date to the transaction date
        # If the item data is from receipt, then check with records starting from the transaction date to +5 days from the transaction date
        try:
            # Get transaction date and ensure it's a datetime object
            transaction_date_str = item_data_object.get("TxnDate")
            if not transaction_date_str:
                chat_log.error("No transaction_date found in item_data_object")
                return None

            # Convert string to datetime if needed
            if isinstance(transaction_date_str, str):
                try:
                    transaction_date = datetime.strptime(transaction_date_str, "%Y-%m-%d").date()
                except ValueError:
                    try:
                        transaction_date = datetime.strptime(transaction_date_str, "%Y-%m-%d %H:%M:%S").date()
                    except ValueError:
                        try:
                            transaction_date = datetime.strptime(transaction_date_str, "%Y-%m-%dT%H:%M:%SZ").date()
                        except ValueError:
                            chat_log.error(f"Unable to parse transaction_date: {transaction_date_str}")
                            return None
            else:
                transaction_date = transaction_date_str

            # Calculate date range
            from_date = transaction_date - timedelta(days=5) if is_from_statement else transaction_date
            to_date = (
                transaction_date + timedelta(days=5) if is_from_statement else transaction_date + timedelta(days=5)
            )

            # Convert dates to strings for SQL query
            from_date_str = from_date.strftime("%Y-%m-%d")
            to_date_str = to_date.strftime("%Y-%m-%d")
            item_query = f"WHERE TxnDate >= '{from_date_str}' AND TxnDate <= '{to_date_str}'"
            res = quickbooks_query_object(entity_type, item_query)
            if res.get("success") and res.get("data"):
                object_type_records = res["data"].get("QueryResponse", {})
                if object_type_records and entity_type in object_type_records:
                    items_in_range = object_type_records[entity_type]
            # Filter out the items that are already excluded
            potential_match_items = [item for item in items_in_range if item.get("Id") not in exclude_item_ids]

            if not potential_match_items:
                chat_log.debug(
                    f"No potential match {entity_type.lower()} items found in the date range {from_date_str} to {to_date_str} for item done on {transaction_date_str}, excluding items {exclude_item_ids}"
                )
                return None  # No potential match items found in the date range

            # # Filter out the items that amounts are different (more than 5%)
            # total_line_amount = 0
            # for line in item_data_object.get("Line"):
            #     total_line_amount += line.get("Amount")
            # potential_match_items = []
            # for item in items_in_range:
            #     if not item.get("TotalAmt") or not total_line_amount:
            #         continue  # 0 amounts are not valid
            #     if (
            #         abs(item.get("TotalAmt") - total_line_amount) / item_data_object.get("TotalAmt")
            #         < 0.05
            #     ):
            #         potential_match_items.append(item)
            # if not potential_match_items:
            #     chat_log.debug(
            #         f"No potential match found for {entity_type.lower()} item {item_data_object} from the list of {len(items_in_range)} items in the date range {from_date_str} to {to_date_str}"
            #     )
            #     return None  # No potential match found
            # Set PrivateNote for items that don't have it
            for item in potential_match_items:
                if not item.get("PrivateNote"):
                    item["PrivateNote"] = ""
            chat_log.debug(
                f"Found {len(potential_match_items)} potential matches for {entity_type.lower()} item {item_data_object} from the list of {len(items_in_range)} items in the date range {from_date_str} to {to_date_str}, excluding items {exclude_item_ids}"
            )
            # Convert the items array to dataframe
            items_df = pd.DataFrame(potential_match_items)
            # Use cognitive search to find the most similar item
            matching_guidelines = f"1 - TxnDate: should be between {from_date_str} and {to_date_str} if the transaction source in PrivateNote is different, else the TxnDate should be the same.\n\
                2 - TotalAmt: should be within 5% of the amount in the {entity_type.lower()}.\n\
                3 - Line item description: Should intelligently compare by vendor and product/service name based on available information."
            search_query = f"Find the {entity_type.lower()} transaction that matches this:\n {json.dumps(item_data_object)}\n{matching_guidelines}"
            matched_df = intelligent_text_match(
                chat_id, items_df, ["TotalAmt", "TxnDate", "Line", "PrivateNote"], search_query
            )
            if matched_df.empty:
                chat_log.debug(
                    f"No match found for {entity_type.lower()} from the list of {len(potential_match_items)} potential matches"
                )
                return None  # No match found
            # Report the no. of matches found
            chat_log.debug(
                f"Found {len(matched_df)} matches for {entity_type.lower()} from the list of {len(potential_match_items)} potential matches"
            )
            match_records_list = matched_df.to_dict("records")
            chat_log.debug(f"All matches:\n {json.dumps(match_records_list, indent=4)}\n")
            # Return the first match as a dictionary
            return matched_df.iloc[0].to_dict()
        except Exception as e:
            chat_log.error(f"Error finding existing {entity_type.lower()}: {e}: \n{traceback.format_exc()}")
            return None

    """
    Description: Update existing expenses in QuickBooks
    Input: CSV file with following columns:
    1. purchase_id
    2. payment_account_id (optional)
    3. transaction_date (optional)
    4. total_amount (optional)
    5. payment_method_id (optional)
    6. global_tax_calculation (optional)
    Output: List of csv files with reported status of each expense recording
    """

    def update_expenses(
        self, conversation: Conversation, source: str, data_file_path_list: list, task_instruction, chat_log
    ):
        # TODO: Update vendor
        # First validate whether purchase_updates.csv and purchase_line_updates.csv is present in the data_file_path_list
        purchases_csv_path = None
        purchase_lines_csv_path = None
        for data_file_path in data_file_path_list:
            if data_file_path.endswith("purchase_updates.csv"):
                purchases_csv_path = data_file_path
            elif data_file_path.endswith("purchase_line_updates.csv"):
                purchase_lines_csv_path = data_file_path

        if not purchases_csv_path:
            return {
                "execution_result": "Failed to find purchase_updates.csv file. Please check the file names and try again.",
                "csv_paths": [],
            }

        # If line updates file not given, then create it with single line for each purchase in purchase_updates.csv
        if not purchase_lines_csv_path:
            chat_log.debug("No purchase_line_updates.csv file found. Creating one")
            try:
                purchases_df = load_data(conversation.chat_id, "purchase_updates.csv")
                purchase_lines = []
                for _, row in purchases_df.iterrows():
                    purchase_lines.append(
                        {
                            "purchase_id": row["purchase_id"],
                            "line_number": 1,
                            "detail_type": "AccountBasedExpenseLineDetail",
                            "line_amount": row.get("total_amount", ""),
                            "description": row.get("payee_name", ""),
                        }
                    )
                save_data(conversation.chat_id, "purchase_line_updates.csv", purchase_lines)
            except Exception as e:
                chat_log.error(f"Error creating purchase_line_updates.csv: {e}")
                return {
                    "execution_result": f"Error creating purchase_line_updates.csv: {e}",
                    "csv_paths": [],
                }

        chat_id = conversation.chat_id
        # --- Load Data ---
        try:
            purchases_df = load_data(chat_id, "purchase_updates.csv")
            lines_df = load_data(chat_id, "purchase_line_updates.csv")
        except Exception as e:
            chat_log.error(f"Error loading purchase_updates.csv: {e}")
            return {
                "execution_result": f"Error loading purchase_updates.csv: {e}",
                "csv_paths": [],
            }
        # --- Validate mandatory fields (purchase_id) in purchase_updates.csv ---
        if "purchase_id" not in purchases_df.columns:
            return {
                "execution_result": "Missing mandatory field (purchase_id) in purchase_updates.csv",
                "csv_paths": [],
            }
        # --- Validate mandatory fields (purchase_id, line_number) in purchase_line_updates.csv ---
        if "purchase_id" not in lines_df.columns or "line_number" not in lines_df.columns:
            return {
                "execution_result": "Missing mandatory fields (purchase_id, line_number) in purchase_line_updates.csv",
                "csv_paths": [],
            }

        # We can only update payment account id, transaction date, total amount, payment method id, global tax calculation
        if (
            "transaction_date" not in purchases_df.columns
            and "total_amount" not in purchases_df.columns
            and "payment_method_id" not in purchases_df.columns
            and "global_tax_calculation" not in purchases_df.columns
            and "line_amount" not in lines_df.columns
        ):
            return {
                "execution_result": "No valid fields to update in purchase_updates.csv",
                "csv_paths": [],
            }
        # Query existing transactions to get the existing field values
        existing_purchases = self.query_transactions(
            conversation, "Purchase", chat_log, id_list=purchases_df["purchase_id"].tolist()
        )

        results = []
        updated_ids = []
        # Track the first updated at time - in local timezone - in string format: YYYY-MM-DDTHH:MM:SS-HH:MM
        company_tz = ZoneInfo("America/Winnipeg")
        local_time = datetime.now().astimezone(company_tz)
        # Format in ISO 8601 with offset
        first_updated_cutoff = local_time.isoformat()
        csv_output_file_paths = []

        # --- Process Updates ---
        for _, row in purchases_df.iterrows():
            purchase_id = str(row.get("purchase_id"))
            # Load matching existing purchase
            existing_purchase = existing_purchases["records"].get(str(purchase_id))
            if not existing_purchase:
                chat_log.warning(f"Existing purchase not found for purchase_id {purchase_id}")
                continue
            # --- Build update payload with mandatory fields from existing purchase ---
            payload = {
                "Id": purchase_id,
                "SyncToken": existing_purchase["SyncToken"],
                "sparse": True,
                "Line": existing_purchase["Line"],
                "PrivateNote": "source=" + source,
                "PaymentType": existing_purchase["PaymentType"],
                "AccountRef": existing_purchase["AccountRef"],
                "GlobalTaxCalculation": existing_purchase["GlobalTaxCalculation"],
            }
            if "payment_account_id" in purchases_df.columns:
                payload["AccountRef"] = {"value": row.get("payment_account_id")}
            if "transaction_date" in purchases_df.columns:
                payload["TxnDate"] = str(row.get("transaction_date"))
            if "payment_method_id" in purchases_df.columns:
                payload["PaymentMethodRef"] = {"value": row.get("payment_method_id")}
            if "global_tax_calculation" in purchases_df.columns:
                payload["GlobalTaxCalculation"] = row.get("global_tax_calculation")

            # Fill the line items
            for _, line_row in lines_df.iterrows():
                if line_row.get("purchase_id") == purchase_id:
                    line_obj = {
                        "Id": line_row.get("line_number"),
                        "Amount": float(line_row.get("line_amount")),
                    }
                    if line_row.get("description") not in [None, ""]:
                        line_obj["Description"] = line_row.get("description")
                    if line_row.get("detail_type") not in [None, ""]:
                        line_obj["DetailType"] = line_row.get("detail_type")
                    if line_row.get("expense_account_id") not in [None, ""]:
                        line_obj["AccountBasedExpenseLineDetail"] = {
                            "AccountRef": {"value": line_row.get("expense_account_id")}
                        }
                    if line_row.get("tax_code_id") not in [None, ""]:
                        line_obj["TaxCodeRef"] = {"value": line_row.get("tax_code_id")}
                    payload["Line"].append(line_obj)

            # --- Update Purchase API call ---
            try:
                res = quickbooks_update_purchase(payload)
            except Exception as e:
                chat_log.error(
                    f"Error updating purchase {purchase_id}: {e}, payload: {payload}\n{traceback.format_exc()}"
                )
                res = {"success": False, "message": str(e)}
                results.append({"purchase_id": purchase_id, "status": "Failed", "error": str(e)})
                continue
            if not res.get("success"):
                results.append(
                    {"purchase_id": purchase_id, "status": "Failed", "error": res.get("message", "Unknown error")}
                )
                continue

            results.append({"purchase_id": purchase_id, "status": "Success", "error": ""})

            updated_ids.append(purchase_id)

        results_df = pd.DataFrame(results)
        # Do a verification call to QBO API to check whether the purchase records are successfully updated
        updated_entries_file_name, n_verified = self.review_updated_entries(
            chat_id, "Purchase", first_updated_cutoff, updated_ids, chat_log
        )
        if updated_entries_file_name:
            conversation.data_reference_set.add(updated_entries_file_name)
            csv_output_file_paths.append(f"storage/public/{chat_id}/files/{updated_entries_file_name}")

        # Provide summary
        n_success = sum(r["status"] == "Success" for r in results)
        n_failed = sum(r["status"] == "Failed" for r in results)
        chat_log.info(
            f"{n_success} success, {n_failed} failed, {n_verified} verified line items, result summary: \n{results_df.to_markdown(index=False)}"
        )
        return {
            "execution_result": (
                f"{n_success} success, {n_failed} failed, expense update summary: \n{results_df.to_markdown(index=False)} \n\n{n_verified} Verification of updated line items in QBO — see review_updated_expenses.csv"
                if updated_entries_file_name
                else "Verification of updated entries failed."
            ),
            "csv_paths": csv_output_file_paths,
        }

    """
    Description: Update line items of existing expenses in QuickBooks
    Input: CSV file with following columns:
    1. purchase_id
    2. line_index
    3. line_amount (optional)
    4. description (optional)
    5. expense_account_id (optional)
    6. tax_code_id (optional)
    Output: List of csv files with reported status of each expense recording

    """

    def update_expense_lines(self, conversation: Conversation, data_file_path_list: list, task_instruction, chat_log):
        # First validate whether purchase_line_updates.csv is present in the data_file_path_list
        purchase_lines_csv_path = None
        for data_file_path in data_file_path_list:
            if data_file_path.endswith("purchase_line_updates.csv"):
                purchase_lines_csv_path = data_file_path

        if not purchase_lines_csv_path:
            return {
                "execution_result": "Failed to find purchase_line_updates.csv file. Please check the file names and try again.",
                "csv_paths": [],
            }

        chat_id = conversation.chat_id
        # --- Load Data ---
        try:
            lines_df = load_data(chat_id, "purchase_line_updates.csv")
        except Exception as e:
            chat_log.error(f"Error loading purchase_line_updates.csv: {e}")
            return {
                "execution_result": f"Error loading purchase_line_updates.csv: {e}",
                "csv_paths": [],
            }

        # --- Validate mandatory fields (purchase_id, line index) in purchase_line_updates.csv ---
        if "purchase_id" not in lines_df.columns or "line_index" not in lines_df.columns:
            return {
                "execution_result": "Missing mandatory fields (purchase_id, line_index) in purchase_line_updates.csv",
                "csv_paths": [],
            }
        # We can only update line amount, description, expense account id, tax code id
        if (
            "line_amount" not in lines_df.columns
            and "description" not in lines_df.columns
            and "expense_account_id" not in lines_df.columns
            and "tax_code_id" not in lines_df.columns
        ):
            return {
                "execution_result": "No valid fields to update in purchase_line_updates.csv",
                "csv_paths": [],
            }
        results = []
        updated_ids = []
        # Track the first updated at time - in local timezone - in string format: YYYY-MM-DDTHH:MM:SS-HH:MM
        company_tz = ZoneInfo("America/Winnipeg")
        local_time = datetime.now().astimezone(company_tz)
        # Format in ISO 8601 with offset
        first_updated_cutoff = local_time.isoformat()
        csv_output_file_paths = []
        # --- Process Updates ---
        for _, row in lines_df.iterrows():
            purchase_id = row.get("purchase_id")
            line_index = row.get("line_index")
            # --- Build update payload ---
            payload = {
                "Id": purchase_id,
                "sparse": True,
                "Line": [],
            }
            if "line_amount" in lines_df.columns:
                payload["Line"].append({"Amount": float(row.get("line_amount"))})
            if "description" in lines_df.columns:
                payload["Line"].append({"Description": row.get("description")})
            if "expense_account_id" in lines_df.columns:
                payload["Line"].append(
                    {"AccountBasedExpenseLineDetail": {"AccountRef": {"value": row.get("expense_account_id")}}}
                )
            if "tax_code_id" in lines_df.columns:
                payload["Line"].append({"TaxCodeRef": {"value": row.get("tax_code_id")}})

            # --- Update Purchase API call ---
            try:
                res = quickbooks_update_purchase(payload)
            except Exception as e:
                chat_log.error(
                    f"Error updating purchase {purchase_id}: {e}, payload: {payload}\n{traceback.format_exc()}"
                )
                res = {"success": False, "message": str(e)}
                results.append({"purchase_id": purchase_id, "status": "Failed", "error": str(e)})
                continue
            if not res.get("success"):
                results.append(
                    {"purchase_id": purchase_id, "status": "Failed", "error": res.get("message", "Unknown error")}
                )
                continue

            results.append({"purchase_id": purchase_id, "status": "Success", "error": ""})

            updated_ids.append(purchase_id)

        results_df = pd.DataFrame(results)
        # Do a verification call to QBO API to check whether the purchase records are successfully updated
        updated_entries_file_name, n_verified = self.review_updated_entries(
            chat_id, "Purchase", first_updated_cutoff, updated_ids, chat_log
        )
        if updated_entries_file_name:
            conversation.data_reference_set.add(updated_entries_file_name)
            csv_output_file_paths.append(f"storage/public/{chat_id}/files/{updated_entries_file_name}")

        # Provide summary
        n_success = sum(r["status"] == "Success" for r in results)
        n_failed = sum(r["status"] == "Failed" for r in results)
        chat_log.info(
            f"{n_success} success, {n_failed} failed, {n_verified} verified line items, result summary: \n{results_df.to_markdown(index=False)}"
        )
        return {
            "execution_result": (
                f"{n_success} success, {n_failed} failed, expense update summary: \n{results_df.to_markdown(index=False)} \n\n{n_verified} Verification of updated line items in QBO — see review_updated_expenses.csv"
                if updated_entries_file_name
                else "Verification of updated entries failed."
            ),
            "csv_paths": csv_output_file_paths,
        }

    """
    Description: Find the payee id for a purchase - either by matching with existing vendor/customer/employee or creating a new vendor
    Input: payee_keyword: Keyword from the receipt to search for the payee
    Output: payee_id : Id of the vendor/customer/employee found or created
    """

    def find_payee(
        self,
        payee_keyword: str,
        payee_address: str,
        chat_log: Logger,
        input_source="receipt",
        check_entity_list=["Vendor", "Customer", "Employee"],
    ):
        if not payee_keyword:
            return None
        payee_keyword = payee_keyword.lower()
        payee_record = None
        payee_id = None
        if "Vendor" in check_entity_list:
            # First query from available vendors
            query = f"WHERE Active = true AND DisplayName LIKE '%{payee_keyword}%'"
            res = quickbooks_query_object("Vendor", query)
            if res.get("success") and res.get("data"):
                object_type_records = res["data"].get("QueryResponse", {})
                if object_type_records and "Vendor" in object_type_records:
                    payee_record = object_type_records["Vendor"][0]
            if payee_record:
                payee_id = payee_record.get("Id")
                chat_log.info(f"Found vendor {payee_id} for {payee_keyword}")
                # If this is from a receipt/invoice, then update the vendor details if different
                if input_source in ["receipt", "invoice"]:
                    self._check_and_update_vendor_details(payee_record, payee_keyword, payee_address, chat_log)
                return payee_id

        if "Customer" in check_entity_list:
            # Secondly, query for available customers
            query = f"WHERE Active = true AND DisplayName LIKE '%{payee_keyword}%'"
            res = quickbooks_query_object("Customer", query)
            if res.get("success") and res.get("data"):
                object_type_records = res["data"].get("QueryResponse", {})
                if object_type_records and "Customer" in object_type_records:
                    payee_record = object_type_records["Customer"][0]
            if payee_record:
                payee_id = payee_record.get("Id")
                chat_log.info(f"Found customer {payee_id} for {payee_keyword}")
                return payee_id

        if "Employee" in check_entity_list:
            # Thirdly, query for available employees
            query = f"WHERE Active = true AND DisplayName LIKE '%{payee_keyword}%'"
            res = quickbooks_query_object("Employee", query)
            if res.get("success") and res.get("data"):
                object_type_records = res["data"].get("QueryResponse", {})
                if object_type_records and "Employee" in object_type_records:
                    payee_record = object_type_records["Employee"][0]
            if payee_record:
                payee_id = payee_record.get("Id")
                chat_log.info(f"Found employee {payee_id} for {payee_keyword}")
                return payee_id

        # If no match found, create a new vendor
        payload = {
            "DisplayName": payee_keyword,
            "PrintOnCheckName": payee_keyword,
            "BillAddr": {"Line1": payee_address},
        }
        res = quickbooks_create_vendor(payload)
        if res.get("success"):
            payee_id = res.get("createdId")
            chat_log.info(f"Created new vendor {payee_id} for {payee_keyword}")
        else:
            chat_log.info(f"Failed to create new vendor for {payee_keyword}")
        return payee_id

    """
    Description: Retrieve the created entries from QuickBooks and review the status
    Input: 
        entity_type: Type of the entity to review (e.g. Vendor, Bill, Purchase)
        created_at: Time which first of the entries were created in order to query by MetaData.CreateTime
        created_ids: List of ids of the created entries
    Output: CSV file with created entries, no of records created
    """

    def review_created_entries(
        self, chat_id: str, entity_type: str, created_at: str, created_ids: list, chat_log: Logger
    ):
        query = f"WHERE MetaData.CreateTime >= '{created_at}' ORDER BY MetaData.CreateTime ASC"
        res = quickbooks_query_object(entity_type, query)
        if res.get("success") and res.get("data"):
            object_type_records = res["data"].get("QueryResponse", {})
            if object_type_records and entity_type in object_type_records:
                df = self._parse_created_entries(entity_type, object_type_records[entity_type], chat_log)
                df = df[df["Id"].isin(created_ids)]
                if not df.empty:
                    file_name = f"confirmation_of_created_{entity_type.lower()}.csv"
                    out_path = f"storage/public/{chat_id}/files/{file_name}"
                    df.to_csv(out_path, index=False)
                    chat_log.info(f"Review file created at {out_path}")
                    return file_name, len(df)
                else:
                    chat_log.info(f"No created entries found for {entity_type} with ids {created_ids}")
        return None, 0

    def review_updated_entries(
        self, chat_id: str, entity_type: str, updated_at: str, updated_ids: list, chat_log: Logger
    ):
        # Convert id list to quoted string list
        updated_ids_list = [("'" + str(i) + "'") for i in updated_ids]
        query = f"WHERE Id IN ({','.join(updated_ids_list)})"
        res = quickbooks_query_object(entity_type, query)
        if res.get("success") and res.get("data"):
            object_type_records = res["data"].get("QueryResponse", {})
            if object_type_records and entity_type in object_type_records:
                df = self._parse_created_entries(entity_type, object_type_records[entity_type], chat_log)
                if not df.empty:
                    file_name = f"confirmation_of_updated_{entity_type.lower()}.csv"
                    out_path = f"storage/public/{chat_id}/files/{file_name}"
                    df.to_csv(out_path, index=False)
                    chat_log.info(f"Review file created at {out_path}")
                    return file_name, len(df)
                else:
                    chat_log.info(f"No updated entries found for {entity_type} with ids {updated_ids}")
        return None, 0

    """
    Description: Parse the created entries in to user friendly format and return a dataframe
    Input: 
        entity_type: Type of the entity to review (e.g. Vendor, Bill, Purchase)
        records: List of records returned from the query
            eg: [{'AccountRef': {'value': '**********', 'name': 'Rogers Credit Card'}, 'PaymentType': 'CreditCard', 'EntityRef': {'value': '6', 'name': 'Staples Canada', 'type': 'Vendor'}, 'Credit': False, 'TotalAmt': 105.82, 'GlobalTaxCalculation': 'NotApplicable', 'PurchaseEx': {'any': [{'name': '{http://schema.intuit.com/finance/v3}NameValue', 'declaredType': 'com.intuit.schema.finance.v3.NameValue', 'scope': 'javax.xml.bind.JAXBElement$GlobalScope', 'value': {'Name': 'TxnType', 'Value': '54'}, 'nil': False, 'globalScope': True, 'typeSubstituted': False}]}, 'domain': 'QBO', 'sparse': False, 'Id': '41', 'SyncToken': '1', 'MetaData': {'CreateTime': '2025-09-09T11:10:06-07:00', 'LastUpdatedTime': '2025-09-09T11:10:19-07:00'}, 'TxnDate': '2025-09-02', 'CurrencyRef': {'value': 'CAD', 'name': 'Canadian Dollar'}, 'Line': [{'Id': '1', 'Description': 'CANON TR7620A WIRELESS PRINTER', 'Amount': 89.99, 'DetailType': 'AccountBasedExpenseLineDetail', 'AccountBasedExpenseLineDetail': {'AccountRef': {'value': '42', 'name': 'Supplies'}, 'BillableStatus': 'NotBillable', 'TaxCodeRef': {'value': '7'}}, 'CustomExtensions': []}, {'Id': '2', 'Description': 'RECYCLING FEE', 'Amount': 4.5, 'DetailType': 'AccountBasedExpenseLineDetail', 'AccountBasedExpenseLineDetail': {'AccountRef': {'value': '42', 'name': 'Supplies'}, 'BillableStatus': 'NotBillable', 'TaxCodeRef': {'value': '7'}}, 'CustomExtensions': []}], 'TxnTaxDetail': {'TotalTax': 11.33, 'TaxLine': [{'Amount': 4.72, 'DetailType': 'TaxLineDetail', 'TaxLineDetail': {'TaxRateRef': {'value': '3'}, 'PercentBased': True, 'TaxPercent': 5, 'NetAmountTaxable': 94.49}}, {'Amount': 6.61, 'DetailType': 'TaxLineDetail', 'TaxLineDetail': {'TaxRateRef': {'value': '15'}, 'PercentBased': True, 'TaxPercent': 7, 'NetAmountTaxable': 94.49}}]}}, {'AccountRef': {'value': '**********', 'name': 'Rogers Credit Card'}, 'PaymentType': 'CreditCard', 'EntityRef': {'value': '6', 'name': 'Staples Canada', 'type': 'Vendor'}, 'Credit': False, 'TotalAmt': 7.83, 'GlobalTaxCalculation': 'NotApplicable', 'PurchaseEx': {'any': [{'name': '{http://schema.intuit.com/finance/v3}NameValue', 'declaredType': 'com.intuit.schema.finance.v3.NameValue', 'scope': 'javax.xml.bind.JAXBElement$GlobalScope', 'value': {'Name': 'TxnType', 'Value': '54'}, 'nil': False, 'globalScope': True, 'typeSubstituted': False}]}, 'domain': 'QBO', 'sparse': False, 'Id': '42', 'SyncToken': '1', 'MetaData': {'CreateTime': '2025-09-09T11:11:30-07:00', 'LastUpdatedTime': '2025-09-09T11:11:35-07:00'}, 'TxnDate': '2025-09-02', 'CurrencyRef': {'value': 'CAD', 'name': 'Canadian Dollar'}, 'Line': [{'Id': '1', 'Description': 'OB FSC PAPER REAM', 'Amount': 6.99, 'DetailType': 'AccountBasedExpenseLineDetail', 'AccountBasedExpenseLineDetail': {'AccountRef': {'value': '40', 'name': 'Stationery and printing'}, 'BillableStatus': 'NotBillable', 'TaxCodeRef': {'value': '7'}}, 'CustomExtensions': []}], 'TxnTaxDetail': {'TotalTax': 0.84, 'TaxLine': [{'Amount': 0.35, 'DetailType': 'TaxLineDetail', 'TaxLineDetail': {'TaxRateRef': {'value': '3'}, 'PercentBased': True, 'TaxPercent': 5, 'NetAmountTaxable': 6.99}}, {'Amount': 0.49, 'DetailType': 'TaxLineDetail', 'TaxLineDetail': {'TaxRateRef': {'value': '15'}, 'PercentBased': True, 'TaxPercent': 7, 'NetAmountTaxable': 6.99}}]}}]
    Output: Dataframe with parsed records
    """

    def _parse_created_entries(self, entity_type: str, records: list, chat_log: Logger):
        chat_log.info(f"Checking {len(records)} records for {entity_type}")
        parsed_records = []
        for record in records:
            if entity_type == "Purchase":
                # Separate records for each line
                for line in record.get("Line"):
                    tax_lines_str = ""
                    if "TxnTaxDetail" in record and "TaxLine" in record["TxnTaxDetail"]:
                        for tax_line in record["TxnTaxDetail"]["TaxLine"]:
                            tax_lines_str += f"{tax_line['Amount']}@{tax_line['TaxLineDetail']['TaxPercent'] if tax_line['TaxLineDetail'].get('TaxPercent') else ''}, "
                    parsed_record = {
                        "Id": record["Id"],
                        "Payee": record["EntityRef"]["name"] if record.get("EntityRef", {}).get("name") else "",
                        "TxnDate": record.get("TxnDate", ""),
                        "TotalAmt": record.get("TotalAmt"),
                        "LineDescription": line.get("Description"),
                        "LineAmount": line.get("Amount"),
                        "ExpenseAccount": line.get("AccountBasedExpenseLineDetail", {})
                        .get("AccountRef", {})
                        .get("name", ""),
                        "GlobalTaxCalculation": record.get("GlobalTaxCalculation"),
                        "TaxLines": tax_lines_str,
                    }
                    parsed_records.append(parsed_record)
        df = pd.DataFrame(parsed_records)
        chat_log.info(f"Records parsed:\n {parsed_records}")
        return df

    def _check_and_update_vendor_details(self, vendor_record, input_name, input_address, chat_log):
        is_to_update = False
        # Check if the name is different from the vendor record
        if vendor_record.get("DisplayName") != input_name:
            # Update the vendor record
            is_to_update = True
            chat_log.debug(
                f"Updating vendor {vendor_record['Id']} name: {vendor_record['DisplayName']} -> {input_name}"
            )
        # Check if the address is different from the vendor record
        if vendor_record.get("BillAddr", {}).get("Line1") != input_address:
            # Update the vendor record
            is_to_update = True
            chat_log.debug(
                f"Updating vendor {vendor_record['Id']} address: {vendor_record['BillAddr']['Line1']} -> {input_address}"
            )

        if not is_to_update:
            chat_log.debug(f"No change needed for Vendor {vendor_record['Id']}")
            return

        # Update the vendor record
        payload = {
            "Id": vendor_record["Id"],
            "SyncToken": vendor_record["SyncToken"],
            "DisplayName": input_name,
            "BillAddr": {"Line1": input_address},
        }

        res = quickbooks_update_vendor(payload)
        if res.get("success"):
            chat_log.info(f"Updated vendor {vendor_record['Id']}")
        else:
            chat_log.error(f"Failed to update vendor {vendor_record['Id']}: {res.get('message')}")
