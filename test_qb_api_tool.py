import os
import shutil
from utils.constant import LLMAgentType
from models.conversation import Conversation
from models.session import Session
from services.conversation_service import ConversationService
from services.tools.data_processor.accounting_api_agent import AccountingApiAgent


# conversation = ConversationService().load_conversation("68c817bcd8c43154d3a21c6e")
conversation = ConversationService().load_conversation("68c83f4e3b2d065efd05e8a6")
# conversation = ConversationService().load_conversation("68cb68838a39825c0d84b8e9")
api_tool = AccountingApiAgent()

# # Add expenses
# result = api_tool.execute_task(
#     conversation, "receipt", ["purchases.csv", "purchase_lines.csv"], "", "./", {"api_task": "add_expenses"}
# )

# Update purchases
result = api_tool.execute_task(
    conversation,
    "statement",
    ["purchase_updates.csv", "purchase_line_updates.csv"],
    "",
    "./",
    {"api_task": "update_expenses"},
)

# # Query transactions
# result = api_tool.execute_task(
#     conversation,
#     "",
#     [],
#     "",
#     "./",
#     {
#         "api_task": "query_transactions",
#         "data_type_list": ["Purchase"],
#         "from_date": "2025-09-01",
#         "to_date": "2025-09-30",
#     },
# )

# result = api_tool.review_created_entries("Purchase", "2025-09-09T13:07:50.470252-05:00", ["41", "42"], None)

print(result)
